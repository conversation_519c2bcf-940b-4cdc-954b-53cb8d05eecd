'use client'

import React, { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { motion } from 'framer-motion'
import { toast } from 'sonner'
import { 
  ArrowLeft, 
  Play, 
  Clock, 
  Users, 
  Star, 
  BookOpen, 
  Award, 
  CheckCircle,
  Globe,
  Smartphone,
  Download,
  Infinity,
  Share2,
  Heart,
  ShoppingCart
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'

interface CoursePreview {
  id: string
  title: string
  description: string
  price: number
  originalPrice?: number
  slug: string
  thumbnailImage?: string
  category: string
  level: string
  duration: string
  language: string
  instructor: {
    id: string
    name: string
    image?: string
    bio?: string
    rating?: number
    studentsCount?: number
    coursesCount?: number
  }
  rating: number
  reviewsCount: number
  studentsCount: number
  features: string[]
  requirements: string[]
  whatYouWillLearn: string[]
  sections: Array<{
    id: string
    title: string
    lessonsCount: number
    duration: string
    lessons: Array<{
      id: string
      title: string
      type: string
      duration: string
      isFree: boolean
    }>
  }>
  reviews: Array<{
    id: string
    rating: number
    title?: string
    comment?: string
    createdAt: string
    user: {
      id: string
      name: string
      image?: string
    }
  }>
  isEnrolled: boolean
  lastUpdated: string
}

export default function CoursePreviewPage() {
  const params = useParams()
  const router = useRouter()
  const courseSlug = params?.slug as string

  const [course, setCourse] = useState<CoursePreview | null>(null)
  const [loading, setLoading] = useState(true)
  const [enrolling, setEnrolling] = useState(false)
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set())
  const [activeTab, setActiveTab] = useState<'overview' | 'curriculum' | 'instructor' | 'reviews'>('overview')

  useEffect(() => {
    if (courseSlug) {
      fetchCoursePreview()
    }
  }, [courseSlug])

  const fetchCoursePreview = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/courses/preview/${courseSlug}`)
      
      if (!response.ok) {
        throw new Error('Failed to fetch course details')
      }

      const data = await response.json()
      setCourse(data.data.course)
    } catch (error) {
      console.error('Error fetching course preview:', error)
      toast.error('Failed to load course details')
      router.push('/student/courses')
    } finally {
      setLoading(false)
    }
  }

  const handleEnroll = async () => {
    if (!course) return

    if (course.isEnrolled) {
      router.push(`/student/courses/${course.slug}`)
      return
    }

    try {
      setEnrolling(true)
      const response = await fetch('/api/courses/enroll', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ courseId: course.id })
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || 'Failed to enroll')
      }

      const data = await response.json()
      
      if (data.redirectUrl) {
        window.location.href = data.redirectUrl
      } else {
        toast.success('Successfully enrolled in course!')
        router.push(`/student/courses/${course.slug}`)
      }
    } catch (error: any) {
      console.error('Enrollment error:', error)
      toast.error(error.message || 'Failed to enroll in course')
    } finally {
      setEnrolling(false)
    }
  }

  const toggleSection = (sectionId: string) => {
    const newExpanded = new Set(expandedSections)
    if (newExpanded.has(sectionId)) {
      newExpanded.delete(sectionId)
    } else {
      newExpanded.add(sectionId)
    }
    setExpandedSections(newExpanded)
  }

  const handleLessonPreview = async (lessonId: string) => {
    try {
      // Navigate to lesson preview page
      router.push(`/courses/lessons/${lessonId}/preview`)
    } catch (error) {
      console.error('Error opening lesson preview:', error)
      toast.error('Failed to open lesson preview')
    }
  }

  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: course?.title,
        text: course?.description,
        url: window.location.href
      })
    } else {
      navigator.clipboard.writeText(window.location.href)
      toast.success('Course link copied to clipboard!')
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/4 mb-8"></div>
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              <div className="lg:col-span-2">
                <div className="h-64 bg-gray-200 rounded-2xl mb-6"></div>
                <div className="h-6 bg-gray-200 rounded w-3/4 mb-4"></div>
                <div className="h-4 bg-gray-200 rounded w-full mb-2"></div>
                <div className="h-4 bg-gray-200 rounded w-2/3"></div>
              </div>
              <div className="lg:col-span-1">
                <div className="h-96 bg-gray-200 rounded-2xl"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (!course) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-800 dark:text-white mb-4">Course not found</h1>
          <Button onClick={() => router.push('/student/courses')}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Courses
          </Button>
        </div>
      </div>
    )
  }

  const discountPercentage = course.originalPrice 
    ? Math.round(((course.originalPrice - course.price) / course.originalPrice) * 100)
    : 0

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      {/* Header */}
      <div className="bg-white/90 dark:bg-gray-900/90 backdrop-blur-xl border-b border-white/20 dark:border-gray-700/20 sticky top-0 z-50 shadow-lg shadow-blue-500/5">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.3 }}
            >
              <Button
                variant="ghost"
                onClick={() => router.back()}
                className="flex items-center text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-white hover:bg-gray-100/50 dark:hover:bg-gray-800/50 transition-all duration-200"
              >
                <ArrowLeft className="w-5 h-5 mr-2" />
                Back to Courses
              </Button>
            </motion.div>
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.3, delay: 0.1 }}
              className="flex items-center space-x-2"
            >
              <Button
                variant="ghost"
                size="sm"
                onClick={handleShare}
                className="hover:bg-gray-100/50 dark:hover:bg-gray-800/50 transition-all duration-200"
              >
                <Share2 className="w-4 h-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className="hover:bg-gray-100/50 dark:hover:bg-gray-800/50 transition-all duration-200"
              >
                <Heart className="w-4 h-4" />
              </Button>
            </motion.div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2">
            {/* Course Hero */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="mb-8"
            >
              <div className="relative h-64 lg:h-80 rounded-3xl overflow-hidden bg-gradient-to-br from-blue-500 via-purple-500 to-pink-500 mb-6 shadow-2xl shadow-blue-500/20">
                {course.thumbnailImage ? (
                  <img
                    src={course.thumbnailImage}
                    alt={course.title}
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center">
                    <div className="text-center">
                      <Play className="w-20 h-20 text-white/90 mx-auto mb-4" />
                      <p className="text-white/80 text-lg font-medium">Course Preview</p>
                    </div>
                  </div>
                )}
                <div className="absolute inset-0 bg-gradient-to-t from-black/40 via-transparent to-transparent"></div>
                <div className="absolute inset-0 flex items-center justify-center">
                  <motion.div
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <Button
                      size="lg"
                      className="bg-white/20 backdrop-blur-xl hover:bg-white/30 text-white border-white/30 shadow-xl shadow-black/20 px-8 py-4 text-lg font-semibold rounded-2xl transition-all duration-300"
                    >
                      <Play className="w-6 h-6 mr-3" />
                      Preview Course
                    </Button>
                  </motion.div>
                </div>
              </div>

              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.2 }}
                className="flex flex-wrap items-center gap-3 mb-6"
              >
                <Badge variant="secondary" className="bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 px-3 py-1 text-sm font-medium">
                  {course.category}
                </Badge>
                <Badge variant="outline" className="border-purple-200 text-purple-700 dark:border-purple-700 dark:text-purple-300 px-3 py-1 text-sm">
                  {course.level}
                </Badge>
                <Badge variant="outline" className="border-green-200 text-green-700 dark:border-green-700 dark:text-green-300 px-3 py-1 text-sm">
                  <Globe className="w-3 h-3 mr-1" />
                  {course.language}
                </Badge>
              </motion.div>

              <motion.h1
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.3 }}
                className="text-3xl lg:text-5xl font-bold bg-gradient-to-r from-gray-800 via-gray-900 to-gray-800 dark:from-white dark:via-gray-100 dark:to-white bg-clip-text text-transparent mb-6 leading-tight"
              >
                {course.title}
              </motion.h1>

              <motion.p
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.4 }}
                className="text-lg lg:text-xl text-gray-600 dark:text-gray-300 mb-8 leading-relaxed"
              >
                {course.description}
              </motion.p>

              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.5 }}
                className="flex flex-wrap items-center gap-6 text-sm text-gray-600 dark:text-gray-300"
              >
                <div className="flex items-center bg-yellow-50 dark:bg-yellow-900/20 px-3 py-2 rounded-full">
                  <Star className="w-4 h-4 text-yellow-500 fill-current mr-2" />
                  <span className="font-semibold text-yellow-700 dark:text-yellow-300">{course.rating.toFixed(1)}</span>
                  <span className="ml-1 text-gray-600 dark:text-gray-400">({course.reviewsCount.toLocaleString()} reviews)</span>
                </div>
                <div className="flex items-center bg-blue-50 dark:bg-blue-900/20 px-3 py-2 rounded-full">
                  <Users className="w-4 h-4 text-blue-500 mr-2" />
                  <span className="font-semibold text-blue-700 dark:text-blue-300">{course.studentsCount.toLocaleString()}</span>
                  <span className="ml-1 text-gray-600 dark:text-gray-400">students</span>
                </div>
                <div className="flex items-center bg-purple-50 dark:bg-purple-900/20 px-3 py-2 rounded-full">
                  <Clock className="w-4 h-4 text-purple-500 mr-2" />
                  <span className="font-semibold text-purple-700 dark:text-purple-300">{course.duration}</span>
                </div>
                <div className="flex items-center bg-green-50 dark:bg-green-900/20 px-3 py-2 rounded-full">
                  <BookOpen className="w-4 h-4 text-green-500 mr-2" />
                  <span className="text-green-700 dark:text-green-300">Updated {course.lastUpdated}</span>
                </div>
              </motion.div>
            </motion.div>

            {/* Navigation Tabs */}
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.6 }}
              className="border-b border-gray-200 dark:border-gray-700 mb-8"
            >
              <nav className="flex space-x-8">
                {[
                  { id: 'overview', label: 'Overview', icon: BookOpen },
                  { id: 'curriculum', label: 'Curriculum', icon: Play },
                  { id: 'instructor', label: 'Instructor', icon: Users },
                  { id: 'reviews', label: 'Reviews', icon: Star }
                ].map((tab) => {
                  const Icon = tab.icon
                  return (
                    <motion.button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id as any)}
                      className={`py-4 px-2 border-b-2 font-medium text-sm transition-all duration-300 flex items-center space-x-2 ${
                        activeTab === tab.id
                          ? 'border-blue-500 text-blue-600 dark:text-blue-400 bg-blue-50/50 dark:bg-blue-900/20 rounded-t-lg'
                          : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 hover:bg-gray-50/50 dark:hover:bg-gray-800/50 rounded-t-lg'
                      }`}
                      whileHover={{ y: -2 }}
                      whileTap={{ y: 0 }}
                    >
                      <Icon className="w-4 h-4" />
                      <span>{tab.label}</span>
                    </motion.button>
                  )
                })}
              </nav>
            </motion.div>

            {/* Tab Content */}
            <motion.div
              key={activeTab}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
            >
              {activeTab === 'overview' && (
                <div className="space-y-8">
                  {/* What You'll Learn */}
                  <Card className="bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl border-0 shadow-xl shadow-blue-500/5 rounded-3xl">
                    <CardContent className="p-8">
                      <h3 className="text-2xl font-bold text-gray-800 dark:text-white mb-6 flex items-center">
                        <CheckCircle className="w-6 h-6 text-green-500 mr-3" />
                        What you'll learn
                      </h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {course.whatYouWillLearn.map((item, index) => (
                          <motion.div
                            key={index}
                            initial={{ opacity: 0, x: -10 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ duration: 0.3, delay: index * 0.1 }}
                            className="flex items-start bg-green-50 dark:bg-green-900/20 p-4 rounded-xl hover:bg-green-100 dark:hover:bg-green-900/30 transition-colors duration-200"
                          >
                            <CheckCircle className="w-5 h-5 text-green-500 mr-3 mt-0.5 flex-shrink-0" />
                            <span className="text-gray-700 dark:text-gray-300 font-medium">{item}</span>
                          </motion.div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>

                  {/* Requirements */}
                  <Card className="bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl border-0 shadow-xl shadow-blue-500/5 rounded-3xl">
                    <CardContent className="p-8">
                      <h3 className="text-2xl font-bold text-gray-800 dark:text-white mb-6 flex items-center">
                        <BookOpen className="w-6 h-6 text-blue-500 mr-3" />
                        Requirements
                      </h3>
                      <ul className="space-y-3">
                        {course.requirements.map((requirement, index) => (
                          <motion.li
                            key={index}
                            initial={{ opacity: 0, x: -10 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ duration: 0.3, delay: index * 0.1 }}
                            className="flex items-start bg-blue-50 dark:bg-blue-900/20 p-4 rounded-xl hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors duration-200"
                          >
                            <span className="w-2 h-2 bg-blue-500 rounded-full mr-4 mt-3 flex-shrink-0"></span>
                            <span className="text-gray-700 dark:text-gray-300 font-medium">{requirement}</span>
                          </motion.li>
                        ))}
                      </ul>
                    </CardContent>
                  </Card>

                  {/* Course Features */}
                  <Card className="bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl border-0 shadow-xl shadow-blue-500/5 rounded-3xl">
                    <CardContent className="p-8">
                      <h3 className="text-2xl font-bold text-gray-800 dark:text-white mb-6 flex items-center">
                        <Award className="w-6 h-6 text-purple-500 mr-3" />
                        This course includes
                      </h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {course.features.map((feature, index) => (
                          <motion.div
                            key={index}
                            initial={{ opacity: 0, scale: 0.9 }}
                            animate={{ opacity: 1, scale: 1 }}
                            transition={{ duration: 0.3, delay: index * 0.1 }}
                            className="flex items-center bg-purple-50 dark:bg-purple-900/20 p-4 rounded-xl hover:bg-purple-100 dark:hover:bg-purple-900/30 transition-colors duration-200"
                          >
                            <CheckCircle className="w-5 h-5 text-purple-500 mr-3" />
                            <span className="text-gray-700 dark:text-gray-300 font-medium">{feature}</span>
                          </motion.div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                </div>
              )}

              {activeTab === 'curriculum' && (
                <Card className="bg-white/90 dark:bg-gray-900/90 backdrop-blur-sm border-0 shadow-lg">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between mb-6">
                      <h3 className="text-xl font-bold text-gray-800 dark:text-white">
                        Course Content
                      </h3>
                      <div className="text-sm text-gray-600 dark:text-gray-400">
                        {course.sections.length} sections • {course.sections.reduce((acc, section) => acc + section.lessonsCount, 0)} lectures • {course.duration}
                      </div>
                    </div>

                    <div className="space-y-2">
                      {course.sections.map((section) => (
                        <div key={section.id} className="border border-gray-200 dark:border-gray-700 rounded-lg">
                          <button
                            onClick={() => toggleSection(section.id)}
                            className="w-full p-4 text-left hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors rounded-lg"
                          >
                            <div className="flex items-center justify-between">
                              <div>
                                <h4 className="font-medium text-gray-800 dark:text-white">
                                  {section.title}
                                </h4>
                                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                                  {section.lessonsCount} lectures • {section.duration}
                                </p>
                              </div>
                              <div className="text-gray-400">
                                {expandedSections.has(section.id) ? '−' : '+'}
                              </div>
                            </div>
                          </button>

                          {expandedSections.has(section.id) && (
                            <div className="border-t border-gray-200 dark:border-gray-700">
                              {section.lessons.map((lesson) => (
                                <div
                                  key={lesson.id}
                                  className={`p-4 border-b border-gray-100 dark:border-gray-800 last:border-b-0 ${
                                    lesson.isFree ? 'cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors' : ''
                                  }`}
                                  onClick={() => lesson.isFree && handleLessonPreview(lesson.id)}
                                >
                                  <div className="flex items-center justify-between">
                                    <div className="flex items-center">
                                      <Play className={`w-4 h-4 mr-3 ${lesson.isFree ? 'text-green-500' : 'text-gray-400'}`} />
                                      <div>
                                        <p className="text-sm font-medium text-gray-800 dark:text-white">
                                          {lesson.title}
                                        </p>
                                        <div className="flex items-center space-x-2 mt-1">
                                          <Badge variant="outline" className="text-xs">
                                            {lesson.type}
                                          </Badge>
                                          {lesson.isFree && (
                                            <Badge variant="secondary" className="text-xs bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300">
                                              Free Preview
                                            </Badge>
                                          )}
                                        </div>
                                      </div>
                                    </div>
                                    <div className="flex items-center space-x-2">
                                      <span className="text-sm text-gray-600 dark:text-gray-400">
                                        {lesson.duration}
                                      </span>
                                      {lesson.isFree && (
                                        <Play className="w-3 h-3 text-green-500" />
                                      )}
                                    </div>
                                  </div>
                                </div>
                              ))}
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}

              {activeTab === 'instructor' && (
                <Card className="bg-white/90 dark:bg-gray-900/90 backdrop-blur-sm border-0 shadow-lg">
                  <CardContent className="p-6">
                    <div className="flex items-start space-x-4">
                      <Avatar className="w-16 h-16">
                        <AvatarImage src={course.instructor.image} alt={course.instructor.name} />
                        <AvatarFallback>
                          {course.instructor.name.split(' ').map(n => n[0]).join('')}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex-1">
                        <h3 className="text-xl font-bold text-gray-800 dark:text-white mb-2">
                          {course.instructor.name}
                        </h3>
                        <div className="flex flex-wrap items-center gap-4 text-sm text-gray-600 dark:text-gray-400 mb-4">
                          {course.instructor.rating && (
                            <div className="flex items-center">
                              <Star className="w-4 h-4 text-yellow-400 fill-current mr-1" />
                              <span>{course.instructor.rating.toFixed(1)} Instructor Rating</span>
                            </div>
                          )}
                          {course.instructor.studentsCount && (
                            <div className="flex items-center">
                              <Users className="w-4 h-4 mr-1" />
                              <span>{course.instructor.studentsCount.toLocaleString()} Students</span>
                            </div>
                          )}
                          {course.instructor.coursesCount && (
                            <div className="flex items-center">
                              <BookOpen className="w-4 h-4 mr-1" />
                              <span>{course.instructor.coursesCount} Courses</span>
                            </div>
                          )}
                        </div>
                        {course.instructor.bio && (
                          <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
                            {course.instructor.bio}
                          </p>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}

              {activeTab === 'reviews' && (
                <Card className="bg-white/90 dark:bg-gray-900/90 backdrop-blur-sm border-0 shadow-lg">
                  <CardContent className="p-6">
                    <div className="flex items-center gap-3 mb-6">
                      <Star className="w-6 h-6 text-yellow-500" />
                      <h3 className="text-xl font-semibold text-gray-800 dark:text-white">
                        Student Reviews ({course?.reviewsCount || 0})
                      </h3>
                    </div>

                    {course?.reviews && course.reviews.length > 0 ? (
                      <div className="space-y-6">
                        {course.reviews.map((review) => (
                          <div key={review.id} className="border-b border-gray-200 dark:border-gray-700 pb-6 last:border-b-0">
                            <div className="flex items-start gap-4">
                              <div className="flex-shrink-0">
                                {review.user.image ? (
                                  <img
                                    src={review.user.image}
                                    alt={review.user.name}
                                    className="w-10 h-10 rounded-full"
                                  />
                                ) : (
                                  <div className="w-10 h-10 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white font-medium">
                                    {review.user.name.charAt(0).toUpperCase()}
                                  </div>
                                )}
                              </div>
                              <div className="flex-1">
                                <div className="flex items-center gap-2 mb-2">
                                  <h4 className="font-medium text-gray-800 dark:text-white">
                                    {review.user.name}
                                  </h4>
                                  <div className="flex items-center gap-1">
                                    {[...Array(5)].map((_, i) => (
                                      <Star
                                        key={i}
                                        className={`w-4 h-4 ${
                                          i < review.rating
                                            ? 'text-yellow-500 fill-current'
                                            : 'text-gray-300'
                                        }`}
                                      />
                                    ))}
                                  </div>
                                  <span className="text-sm text-gray-500">
                                    {new Date(review.createdAt).toLocaleDateString()}
                                  </span>
                                </div>
                                {review.title && (
                                  <h5 className="font-medium text-gray-800 dark:text-white mb-2">
                                    {review.title}
                                  </h5>
                                )}
                                {review.comment && (
                                  <p className="text-gray-600 dark:text-gray-400 leading-relaxed">
                                    {review.comment}
                                  </p>
                                )}
                              </div>
                            </div>
                          </div>
                        ))}

                        {course.reviewsCount > 5 && (
                          <div className="text-center pt-4">
                            <button className="text-blue-600 hover:text-blue-700 font-medium">
                              View all {course.reviewsCount} reviews
                            </button>
                          </div>
                        )}
                      </div>
                    ) : (
                      <div className="text-center py-12">
                        <Star className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                        <h3 className="text-lg font-medium text-gray-800 dark:text-white mb-2">
                          No Reviews Yet
                        </h3>
                        <p className="text-gray-600 dark:text-gray-400">
                          Be the first to review this course after enrolling!
                        </p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              )}
            </motion.div>
          </div>

          {/* Sidebar - Course Purchase Card */}
          <div className="lg:col-span-1">
            <div className="sticky top-24">
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.3 }}
              >
                <Card className="bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl border-0 shadow-2xl shadow-blue-500/10 rounded-3xl overflow-hidden">
                  <CardContent className="p-8">
                    <div className="text-center mb-8">
                      <div className="flex items-center justify-center space-x-3 mb-3">
                        {course.originalPrice && (
                          <span className="text-xl text-gray-500 line-through font-medium">
                            ${course.originalPrice}
                          </span>
                        )}
                        <span className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                          ${course.price}
                        </span>
                        {discountPercentage > 0 && (
                          <Badge variant="destructive" className="bg-red-500 text-white px-3 py-1 text-sm font-bold animate-pulse">
                            {discountPercentage}% OFF
                          </Badge>
                        )}
                      </div>
                      <div className="bg-green-50 dark:bg-green-900/20 px-4 py-2 rounded-full inline-block">
                        <p className="text-sm text-green-700 dark:text-green-300 font-medium flex items-center">
                          <CheckCircle className="w-4 h-4 mr-2" />
                          30-Day Money-Back Guarantee
                        </p>
                      </div>
                    </div>

                  <motion.div
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <Button
                      onClick={handleEnroll}
                      disabled={enrolling}
                      className="w-full mb-6 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 hover:from-blue-600 hover:via-purple-600 hover:to-pink-600 text-white shadow-xl shadow-blue-500/25 hover:shadow-2xl hover:shadow-blue-500/30 transition-all duration-300 rounded-2xl py-4 text-lg font-semibold"
                      size="lg"
                    >
                      {enrolling ? (
                        <div className="flex items-center">
                          <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-3"></div>
                          Processing...
                        </div>
                      ) : course.isEnrolled ? (
                        <>
                          <Play className="w-5 h-5 mr-3" />
                          Continue Learning
                        </>
                      ) : (
                        <>
                          <ShoppingCart className="w-5 h-5 mr-3" />
                          Enroll Now
                        </>
                      )}
                    </Button>
                  </motion.div>

                  <div className="space-y-4 text-sm">
                    <div className="flex items-center text-gray-700 dark:text-gray-300 bg-gray-50 dark:bg-gray-800/50 p-3 rounded-xl">
                      <Infinity className="w-5 h-5 mr-3 text-blue-500" />
                      <span className="font-medium">Full lifetime access</span>
                    </div>
                    <div className="flex items-center text-gray-700 dark:text-gray-300 bg-gray-50 dark:bg-gray-800/50 p-3 rounded-xl">
                      <Smartphone className="w-5 h-5 mr-3 text-green-500" />
                      <span className="font-medium">Access on mobile and TV</span>
                    </div>
                    <div className="flex items-center text-gray-700 dark:text-gray-300 bg-gray-50 dark:bg-gray-800/50 p-3 rounded-xl">
                      <Download className="w-5 h-5 mr-3 text-purple-500" />
                      <span className="font-medium">Downloadable resources</span>
                    </div>
                    <div className="flex items-center text-gray-700 dark:text-gray-300 bg-gray-50 dark:bg-gray-800/50 p-3 rounded-xl">
                      <Award className="w-5 h-5 mr-3 text-yellow-500" />
                      <span className="font-medium">Certificate of completion</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
              </motion.div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
